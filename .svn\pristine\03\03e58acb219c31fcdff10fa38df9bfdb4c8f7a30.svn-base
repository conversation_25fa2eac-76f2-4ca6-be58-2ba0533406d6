// Global variables
let captureStep = 0; // 0: not started, 1: in progress, 2: completed
let capturedImages = {
    card: false,
    face: false
};

// Camera status tracking
let cameraStatus = {
    0: false, // business card camera (left)
    1: false  // face camera (right)
};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM loaded, initializing...');

    const captureBtn = document.getElementById('singleCaptureBtn');
    if (captureBtn) {
        captureBtn.disabled = false;
        captureBtn.addEventListener('click', singleCapture);
        console.log('✅ Capture button enabled and event listener added');
    }

    const generateBtn = document.getElementById('generateBtn');
    const retakeBtn = document.getElementById('retakeBtn');

    if (generateBtn) {
        generateBtn.addEventListener('click', generateAI);
    }
    if (retakeBtn) {
        retakeBtn.addEventListener('click', retakeAll);
    }

    setTimeout(() => {
        initializeCameraStatus();
        checkCameraAvailability();
        loadPromptTemplates();
        updateGenerateButton();
    }, 1000);
});

// Main capture function - captures both cameras simultaneously
function singleCapture() {
    console.log('🔘 singleCapture() called - capturing both cameras simultaneously');

    const btn = document.getElementById('singleCaptureBtn');
    if (!btn) {
        console.error('❌ singleCaptureBtn not found!');
        return;
    }

    if (btn.disabled) {
        console.log('⚠️ Button is disabled, not capturing');
        return;
    }

    console.log('📸 Starting simultaneous capture process...');

    // Update workflow progress for single page layout
    if (typeof updateWorkflowProgress === 'function') {
        updateWorkflowProgress(1, 'processing');
    }

    btn.innerHTML = '<span>⏳</span> Đang chụp cả 2 camera...';
    btn.disabled = true;

    showMessage('📸 Đang chụp cả business card và khuôn mặt...', 'processing');

    // Chụp đồng thời cả 2 camera
    let captureCount = 0;
    let captureErrors = [];

    const onCaptureComplete = () => {
        captureCount++;
        console.log(`✅ Capture ${captureCount}/2 completed`);

        if (captureCount === 2) {
            if (captureErrors.length === 0) {
                console.log('✅ Both cameras captured successfully!');
                captureStep = 2;
                btn.style.display = 'none';
                showMessage('✅ Đã chụp xong cả 2 ảnh!', 'success');

                const retakeBtn = document.getElementById('retakeBtn');
                if (retakeBtn) {
                    retakeBtn.style.display = 'inline-block';
                }

                updateGenerateButton();

                // Update workflow progress for single page layout
                if (typeof updateWorkflowProgress === 'function') {
                    updateWorkflowProgress(2, 'pending');
                    // Load any OCR results
                    setTimeout(() => {
                        if (typeof loadAndDisplayResults === 'function') {
                            loadAndDisplayResults();
                        }
                    }, 1000);
                }
            } else {
                console.error('❌ Some captures failed:', captureErrors);
                showMessage(`❌ Lỗi chụp ảnh: ${captureErrors.join(', ')}`, 'error');

                // Reset button on error
                btn.innerHTML = '<span class="btn-icon">📸</span><span class="btn-text">Chụp ảnh</span>';
                btn.disabled = false;

                // Update workflow progress on error
                if (typeof updateWorkflowProgress === 'function') {
                    updateWorkflowProgress(1, 'error');
                }
            }
        }
    };

    const onCaptureError = (cameraId, error) => {
        captureErrors.push(`Camera ${cameraId}: ${error}`);
        onCaptureComplete();
    };

    // Smart camera selection with robust error handling
    if (window.webrtcCamera && window.webrtcCamera.isInitialized) {
        console.log('📱 Using client-side WebRTC cameras');

        // Capture both cameras using WebRTC with proper error handling
        let completedCount = 0;
        let hasError = false;

        const handleWebRTCComplete = () => {
            completedCount++;
            if (completedCount === 2 && !hasError) {
                console.log('✅ Both WebRTC cameras captured successfully');
                // Update UI to show completion
                captureStep = 2;
                btn.style.display = 'none';
                showMessage('✅ Đã chụp xong cả 2 ảnh!', 'success');

                const retakeBtn = document.getElementById('retakeBtn');
                if (retakeBtn) {
                    retakeBtn.style.display = 'inline-block';
                }
                updateGenerateButton();
            }
        };

        const handleWebRTCError = (cameraId, error) => {
            hasError = true;
            console.error(`❌ WebRTC camera ${cameraId} failed:`, error);

            // Show user-friendly error message
            showMessage(`❌ Camera ${cameraId === 0 ? 'Business Card' : 'Face'} capture failed. Please try again.`, 'error');

            // Reset button
            btn.innerHTML = '<span class="btn-icon">📸</span><span class="btn-text">Chụp ảnh</span>';
            btn.disabled = false;
        };

        // Capture camera 0 (business card)
        window.captureImageWebRTC(0)
            .then(() => handleWebRTCComplete())
            .catch((error) => handleWebRTCError(0, error));

        // Capture camera 1 (face)
        window.captureImageWebRTC(1)
            .then(() => handleWebRTCComplete())
            .catch((error) => handleWebRTCError(1, error));

    } else {
        console.log('🖥️ Using server-side cameras (fallback)');

        // Check if server cameras are available
        fetch('/camera_status')
            .then(response => response.json())
            .then(status => {
                if (status.webrtc_only_mode && (!window.webrtcCamera || !window.webrtcCamera.isInitialized)) {
                    // Server is WebRTC-only but WebRTC failed
                    showMessage('❌ Camera access required. Please allow camera permissions and refresh the page.', 'error');
                    btn.innerHTML = '<span class="btn-icon">📸</span><span class="btn-text">Chụp ảnh</span>';
                    btn.disabled = false;
                    return;
                }

                // Use server cameras
                captureImageSimultaneous(0, onCaptureComplete, onCaptureError);
                captureImageSimultaneous(1, onCaptureComplete, onCaptureError);
            })
            .catch(error => {
                console.error('❌ Cannot check camera status:', error);
                // Try server cameras anyway
                captureImageSimultaneous(0, onCaptureComplete, onCaptureError);
                captureImageSimultaneous(1, onCaptureComplete, onCaptureError);
            });
    }
}

// Capture individual camera for simultaneous capture
function captureImageSimultaneous(cameraId, successCallback, errorCallback) {
    console.log(`📸 captureImageSimultaneous(${cameraId}) called`);

    const cameraName = cameraId === 0 ? 'card' : 'face'; // Camera 0 = card (left), Camera 1 = face (right)
    const statusElement = document.getElementById(`status${cameraId}`);
    const imgTag = document.getElementById(`cam${cameraId}`);

    if (statusElement) {
        statusElement.style.background = '#ffa500';
    }

    fetch(`/capture_step/${cameraId}`, {
        method: 'POST'
    })
    .then(res => res.json())
    .then(data => {
        if (data.status === 'success') {
            // Freeze camera with captured image
            if (imgTag && data.image_path) {
                imgTag.src = '/' + data.image_path + '?t=' + new Date().getTime();
                imgTag.style.border = '3px solid #43e97b';
                console.log(`🖼️ Camera ${cameraId} FROZEN with captured image`);
            }

            if (statusElement) {
                statusElement.style.background = '#43e97b';
                statusElement.className = 'status-dot status-captured';
            }

            // Update status text - SWAPPED
            const statusText = cameraId === 1 ?
                document.getElementById('cardStatus') :
                document.getElementById('faceStatus');
            if (statusText) {
                statusText.textContent = '✅ Đã chụp xong!';
            }

            // Update captured state
            capturedImages[cameraName] = true;
            console.log(`✅ ${cameraName} captured simultaneously`);

            if (successCallback) {
                successCallback();
            }
        } else {
            console.error(`❌ Capture failed for camera ${cameraId}:`, data.message);
            
            if (statusElement) {
                statusElement.style.background = '#ff6b6b';
            }
            
            if (errorCallback) {
                errorCallback(cameraId, data.message);
            }
        }
    })
    .catch(error => {
        console.error(`💥 Network error for camera ${cameraId}:`, error);
        
        if (statusElement) {
            statusElement.style.background = '#ff6b6b';
        }
        
        if (errorCallback) {
            errorCallback(cameraId, error.message);
        }
    });
}

// Generate AI image
function generateAI() {
    console.log('🚀 generateAI() called');

    const generateBtn = document.getElementById('generateBtn');
    if (!generateBtn) {
        console.error('❌ generateBtn not found!');
        return;
    }

    // Kiểm tra trạng thái chính xác
    console.log('🔍 Current state:', {
        captureStep,
        capturedImages,
        buttonDisabled: generateBtn.disabled
    });

    if (captureStep < 2 || !capturedImages.card || !capturedImages.face) {
        console.log('❌ Not ready for generation');
        showMessage('❌ Vui lòng chụp cả 2 ảnh trước khi tạo AI!', 'error');
        return;
    }

    console.log('✅ Ready for generation, starting process...');

    generateBtn.disabled = true;
    generateBtn.innerHTML = '<span>⏳</span> Đang xử lý...';

    showMessage('🔍 Đang tạo ảnh với Gemini 2.0 Flash...', 'processing');

    const promptSelect = document.getElementById('promptSelect');
    const selectedPrompt = promptSelect ? promptSelect.value : 'prompt';

    fetch('/process_images', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            prompt_template: selectedPrompt
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            showMessage('✅ Tạo ảnh thành công!', 'success');

            // Update workflow progress for single page layout
            if (typeof updateWorkflowProgress === 'function') {
                updateWorkflowProgress(3, 'processing');
                setTimeout(() => {
                    if (typeof loadAndDisplayResults === 'function') {
                        loadAndDisplayResults();
                    }
                    updateWorkflowProgress(3, 'complete');
                }, 2000);
            } else if (typeof showResultView === 'function') {
                // Fallback to view switching
                setTimeout(() => {
                    showResultView();
                    if (typeof loadResultData === 'function') {
                        loadResultData();
                    }
                }, 1000);
            } else {
                // Traditional page redirect
                setTimeout(() => {
                    window.location.href = '/result';
                }, 1000);
            }
        } else {
            console.error('❌ Server error:', data.message);
            showMessage(`❌ Lỗi: ${data.message}`, 'error');
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<span>✨</span> Tạo ảnh AI';

            // Update workflow progress on error
            if (typeof updateWorkflowProgress === 'function') {
                updateWorkflowProgress(2, 'error');
            }
        }
    })
    .catch(error => {
        console.error('💥 Network error:', error);
        showMessage('❌ Lỗi kết nối: ' + error.message, 'error');
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<span>✨</span> Tạo ảnh AI';
    });
}

// Retake all photos
function retakeAll() {
    console.log('🔄 retakeAll() called - resetting everything');
    
    // Reset tất cả state
    captureStep = 0;
    capturedImages.card = false;
    capturedImages.face = false;

    const btn = document.getElementById('singleCaptureBtn');
    const instructions = document.getElementById('captureInstructions');
    const retakeBtn = document.getElementById('retakeBtn');

    // Reset capture button
    if (btn) {
        btn.innerHTML = '<span class="btn-icon">📸</span><span class="btn-text">Chụp ảnh</span>';
        btn.disabled = false;
        btn.style.display = 'inline-block';
        console.log('✅ Capture button reset and enabled');
    }

    // Reset instructions
    if (instructions) {
        instructions.innerHTML = 'Nhấn nút để chụp cả 2 camera cùng lúc';
    }

    // Hide retake button
    if (retakeBtn) {
        retakeBtn.style.display = 'none';
    }

    // Restore both camera streams
    restoreStream(0);
    restoreStream(1);

    // Reset status indicators
    resetCameraStatus(0);
    resetCameraStatus(1);

    // Update generate button
    updateGenerateButton();
    
    // Clear any messages
    showMessage('🔄 Đã reset - Sẵn sàng chụp lại!', 'success');
    
    console.log('✅ Complete reset - ready for new simultaneous capture');
}

// Reset camera status
function resetCameraStatus(cameraId) {
    const statusElement = document.getElementById(`status${cameraId}`);
    const statusText = cameraId === 1 ?
        document.getElementById('cardStatus') :
        document.getElementById('faceStatus');

    if (statusElement) {
        statusElement.className = 'status-dot status-ready';
        statusElement.style.background = '';
    }

    if (statusText) {
        const message = cameraId === 1 ?
            '📄 Camera sẵn sàng - Đặt business card' :
            '😊 Camera sẵn sàng - Đặt khuôn mặt';
        statusText.textContent = message;
    }

    console.log(`🔄 Camera ${cameraId} status reset`);
}

// Restore camera stream
function restoreStream(cameraId) {
    const imgTag = document.getElementById(`cam${cameraId}`);
    if (imgTag) {
        // Add timestamp to force refresh
        const timestamp = new Date().getTime();
        imgTag.src = `/video_feed/${cameraId}?t=${timestamp}`;
        imgTag.style.border = '';
        imgTag.style.opacity = '1';
        
        console.log(`🔄 Camera ${cameraId} stream restored to live feed`);
        
        // Force reload after a short delay to ensure stream restoration
        setTimeout(() => {
            if (imgTag.src.includes('/video_feed/')) {
                imgTag.src = `/video_feed/${cameraId}?t=${new Date().getTime()}`;
            }
        }, 500);
    }
}

// Update generate button state
function updateGenerateButton() {
    const generateBtn = document.getElementById('generateBtn');
    if (generateBtn) {
        // Enable button khi đã chụp xong cả 2 ảnh (captureStep = 2)
        const canGenerate = captureStep >= 2 && capturedImages.card && capturedImages.face;
        generateBtn.disabled = !canGenerate;
        
        if (canGenerate) {
            generateBtn.innerHTML = '<span>✨</span> Tạo ảnh AI';
            generateBtn.style.opacity = '1';
            generateBtn.style.cursor = 'pointer';
        } else {
            generateBtn.innerHTML = '<span>⏳</span> Chụp ảnh trước';
            generateBtn.style.opacity = '0.6';
            generateBtn.style.cursor = 'not-allowed';
        }
        
        console.log(`🔘 Generate button updated - step: ${captureStep}, card: ${capturedImages.card}, face: ${capturedImages.face}, enabled: ${canGenerate}`);
    }
}

// Individual camera retake (optional feature)
function retakeCamera(cameraId) {
    console.log(`🔄 Retaking camera ${cameraId}`);
    
    const cameraName = cameraId === 0 ? 'card' : 'face'; // Camera 0 = card, Camera 1 = face
    
    // Reset state cho camera này
    capturedImages[cameraName] = false;
    
    // Restore stream cho camera này
    restoreStream(cameraId);
    
    // Reset status
    resetCameraStatus(cameraId);
    
    // Reset capture step nếu cần
    if (captureStep === 2) {
        captureStep = 1; // Cho phép chụp lại
        
        // Show capture button again
        const btn = document.getElementById('singleCaptureBtn');
        if (btn) {
            btn.style.display = 'inline-block';
            btn.disabled = false;
            btn.innerHTML = cameraId === 1 ?
                '<span>📸</span> Chụp lại Name Card' :
                '<span>👤</span> Chụp lại Khuôn mặt';
        }
        
        // Hide retake button
        const retakeBtn = document.getElementById('retakeBtn');
        if (retakeBtn) {
            retakeBtn.style.display = 'none';
        }
    }
    
    updateGenerateButton();
    showMessage(`🔄 Đã reset camera ${cameraName} - Sẵn sàng chụp lại!`, 'success');
}

// Make function globally accessible
window.retakeCamera = retakeCamera;

// Initialize camera status
function initializeCameraStatus() {
    console.log('🔧 Initializing camera status...');
    
    // Set initial status for both cameras
    const status0 = document.getElementById('status0');
    const status1 = document.getElementById('status1');
    
    if (status0) status0.className = 'status-dot status-ready';
    if (status1) status1.className = 'status-dot status-ready';
    
    console.log('✅ Camera status initialized');
}

// Check camera availability - FIXED VERSION
function checkCameraAvailability() {
    console.log('🔍 Checking camera availability...');

    fetch('/camera_status')
        .then(response => response.json())
        .then(data => {
            console.log('📊 Camera status response:', data);

            // Update camera status based on actual response
            if (data.camera0_available !== undefined && data.camera1_available !== undefined) {
                // New API format with explicit fields
                cameraStatus[0] = data.camera0_available;
                cameraStatus[1] = data.camera1_available;
            } else if (data.cameras) {
                // Fallback to cameras object format
                cameraStatus[0] = data.cameras['0'] ? data.cameras['0'].opened : false;
                cameraStatus[1] = data.cameras['1'] ? data.cameras['1'].opened : false;
            } else {
                // Default to available if unclear
                console.warn('⚠️ Unclear camera status format, defaulting to available');
                cameraStatus[0] = true;
                cameraStatus[1] = true;
            }

            console.log('📊 Updated camera status:', cameraStatus);
            updateCameraStatusDisplay();
        })
        .catch(error => {
            console.error('❌ Error checking camera status:', error);
            // Assume cameras are available if check fails
            cameraStatus[0] = true;
            cameraStatus[1] = true;
            updateCameraStatusDisplay();
        });
}

// Update camera status display - FIXED VERSION
function updateCameraStatusDisplay() {
    const cardStatus = document.getElementById('cardStatus');
    const faceStatus = document.getElementById('faceStatus');
    const status0 = document.getElementById('status0');
    const status1 = document.getElementById('status1');

    // Update Business Card camera (Camera 0) - LEFT SIDE
    if (cardStatus) {
        if (cameraStatus[0]) {
            cardStatus.textContent = '📄 Camera sẵn sàng - Đặt business card';
            cardStatus.style.color = '#43e97b';
        } else {
            cardStatus.textContent = '❌ Business Card camera không khả dụng';
            cardStatus.style.color = '#ff6b6b';
        }
    }

    if (status0) {
        status0.style.background = cameraStatus[0] ? '#43e97b' : '#ff6b6b';
    }

    // Update Face camera (Camera 1) - RIGHT SIDE
    if (faceStatus) {
        if (cameraStatus[1]) {
            faceStatus.textContent = '😊 Camera sẵn sàng - Đặt khuôn mặt';
            faceStatus.style.color = '#43e97b';
        } else {
            faceStatus.textContent = '❌ Face camera không khả dụng';
            faceStatus.style.color = '#ff6b6b';
        }
    }

    if (status1) {
        status1.style.background = cameraStatus[1] ? '#43e97b' : '#ff6b6b';
    }

    // Update capture button - ALLOW CAPTURE EVEN IF ONE CAMERA FAILS
    const captureBtn = document.getElementById('singleCaptureBtn');
    if (captureBtn) {
        // Enable capture if at least one camera is available OR force enable for testing
        const hasAnyCamera = cameraStatus[0] || cameraStatus[1];
        const forceEnable = true; // Force enable for debugging

        if (hasAnyCamera || forceEnable) {
            captureBtn.disabled = false;
            captureBtn.innerHTML = '<span class="btn-icon">📸</span><span class="btn-text">Chụp ảnh</span>';
            captureBtn.style.opacity = '1';

            if (!hasAnyCamera && forceEnable) {
                console.log('⚠️ Force enabling capture button for debugging');
            }
        } else {
            captureBtn.disabled = true;
            captureBtn.innerHTML = '<span>❌</span> Camera không khả dụng';
            captureBtn.style.opacity = '0.6';
        }
    }

    console.log('✅ Camera status display updated');
}

// Load prompt templates - FIXED VERSION
function loadPromptTemplates() {
    console.log('📝 Loading prompt templates...');
    
    const promptSelect = document.getElementById('promptSelect');
    const promptDescription = document.getElementById('promptDescription');
    
    if (!promptSelect) {
        console.error('❌ promptSelect element not found');
        return;
    }

    // Set loading state
    promptSelect.innerHTML = '<option value="">⏳ Đang tải...</option>';
    if (promptDescription) {
        promptDescription.textContent = 'Đang tải danh sách prompt templates...';
    }

    fetch('/api/prompts')
        .then(response => {
            console.log('📥 Prompt API response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('📊 Prompt API data:', data);
            
            if (data.status === 'success' && data.prompts) {
                // Clear loading option
                promptSelect.innerHTML = '';

                // Add prompt options từ object response
                Object.keys(data.prompts).forEach(key => {
                    const prompt = data.prompts[key];
                    const option = document.createElement('option');
                    option.value = key;
                    option.textContent = prompt.name;
                    option.dataset.description = prompt.description;
                    promptSelect.appendChild(option);
                });

                console.log(`✅ Loaded ${Object.keys(data.prompts).length} prompt templates`);

                // Set default selection and description
                if (promptSelect.options.length > 0) {
                    promptSelect.selectedIndex = 0;
                    updatePromptDescription();
                }

                // Add change event listener
                promptSelect.addEventListener('change', updatePromptDescription);
            } else {
                throw new Error('Invalid API response format');
            }
        })
        .catch(error => {
            console.error('❌ Error loading prompts:', error);
            
            // Set fallback options
            promptSelect.innerHTML = `
                <option value="prompt">🏠 Dollhouse (Mặc định)</option>
                <option value="dollhouse">🏠 Dollhouse Miniature</option>
                <option value="professional">💼 Chuyên nghiệp</option>
                <option value="artistic">🎨 Nghệ thuật</option>
                <option value="cartoon">🎭 Hoạt hình</option>
                <option value="cinematic">🎬 Điện ảnh</option>
                <option value="luxury">💎 Sang trọng</option>
            `;
            
            if (promptDescription) {
                promptDescription.innerHTML = '🏠 <strong>Style Dollhouse Miniature</strong><br>Tạo ảnh phong cách ngôi nhà búp bê đáng yêu (Fallback mode)';
            }
            
            // Add change event listener for fallback
            promptSelect.addEventListener('change', updatePromptDescription);
        });
}

// Update prompt description when selection changes
function updatePromptDescription() {
    const promptSelect = document.getElementById('promptSelect');
    const promptDescription = document.getElementById('promptDescription');
    
    if (!promptSelect || !promptDescription) {
        return;
    }

    const selectedOption = promptSelect.options[promptSelect.selectedIndex];
    if (selectedOption && selectedOption.dataset.description) {
        // Use description from API
        const name = selectedOption.textContent;
        const description = selectedOption.dataset.description;
        promptDescription.innerHTML = `<strong>${name}</strong><br>${description}`;
    } else {
        // Fallback descriptions
        const fallbackDescriptions = {
            'prompt': '🏠 <strong>Dollhouse (Mặc định)</strong><br>Style dollhouse miniature cổ điển với môi trường văn phòng thu nhỏ',
            'dollhouse': '🏠 <strong>Dollhouse Miniature</strong><br>Tạo cảnh dollhouse miniature chuyên nghiệp với chi tiết tinh xảo',
            'professional': '💼 <strong>Chuyên nghiệp</strong><br>Ảnh chân dung doanh nhân chuyên nghiệp trong môi trường công sở',
            'artistic': '🎨 <strong>Nghệ thuật</strong><br>Phong cách nghệ thuật sáng tạo với hiệu ứng ánh sáng độc đáo',
            'cartoon': '🎭 <strong>Hoạt hình</strong><br>Phong cách hoạt hình vui nhộn và thân thiện',
            'cinematic': '🎬 <strong>Điện ảnh</strong><br>Phong cách điện ảnh với ánh sáng và góc quay chuyên nghiệp',
            'luxury': '💎 <strong>Sang trọng</strong><br>Phong cách sang trọng và đẳng cấp cao'
        };
        
        const selectedValue = promptSelect.value;
        promptDescription.innerHTML = fallbackDescriptions[selectedValue] || 
            '🎨 <strong>Custom Template</strong><br>Prompt template tùy chỉnh';
    }
    
    console.log(`📝 Updated prompt description for: ${promptSelect.value}`);
}

// Show message to user
function showMessage(message, type = 'info') {
    console.log(`💬 ${type.toUpperCase()}: ${message}`);
    
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.message-popup');
    existingMessages.forEach(msg => msg.remove());
    
    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-popup message-${type}`;
    messageDiv.textContent = message;
    
    // Style the message
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        max-width: 400px;
        word-wrap: break-word;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
    `;
    
    // Set background color based on type
    switch(type) {
        case 'success':
            messageDiv.style.backgroundColor = '#43e97b';
            break;
        case 'error':
            messageDiv.style.backgroundColor = '#ff6b6b';
            break;
        case 'processing':
            messageDiv.style.backgroundColor = '#ffa500';
            break;
        default:
            messageDiv.style.backgroundColor = '#4dabf7';
    }
    
    // Add to page
    document.body.appendChild(messageDiv);
    
    // Auto remove after delay
    const delay = type === 'processing' ? 10000 : 3000;
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.opacity = '0';
            messageDiv.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 300);
        }
    }, delay);
}

// Error handling for images
function handleImageError(imgElement, cameraId) {
    console.error(`❌ Image load error for camera ${cameraId}`);
    
    const statusElement = document.getElementById(`status${cameraId}`);
    if (statusElement) {
        statusElement.style.background = '#ff6b6b';
    }
    
    // Try to reload the stream
    setTimeout(() => {
        if (imgElement) {
            imgElement.src = `/video_feed/${cameraId}?t=${new Date().getTime()}`;
        }
    }, 2000);
}

// Add global error handlers for camera images
document.addEventListener('DOMContentLoaded', function() {
    const cam0 = document.getElementById('cam0');
    const cam1 = document.getElementById('cam1');
    
    if (cam0) {
        cam0.onerror = () => handleImageError(cam0, 0);
    }
    
    if (cam1) {
        cam1.onerror = () => handleImageError(cam1, 1);
    }
});

// ===== COMBINED FILE SUPPORT FUNCTIONS =====

// Function to populate result view with data
function populateResultView(cardInfo, generatedImages) {
    console.log('📄 Populating result view with data:', cardInfo, generatedImages);

    // Populate card info
    if (cardInfo) {
        const cardNameEl = document.getElementById('cardName');
        const cardTitleEl = document.getElementById('cardTitle');
        const cardCompanyEl = document.getElementById('cardCompany');
        const cardEmailEl = document.getElementById('cardEmail');
        const cardPhoneEl = document.getElementById('cardPhone');
        const cardAddressEl = document.getElementById('cardAddress');

        if (cardNameEl) cardNameEl.textContent = cardInfo.name || 'N/A';
        if (cardTitleEl) cardTitleEl.textContent = cardInfo.title || 'N/A';
        if (cardCompanyEl) cardCompanyEl.textContent = cardInfo.company || 'N/A';
        if (cardEmailEl) cardEmailEl.textContent = cardInfo.email || 'N/A';
        if (cardPhoneEl) cardPhoneEl.textContent = cardInfo.phone || 'N/A';
        if (cardAddressEl) cardAddressEl.textContent = cardInfo.address || 'N/A';

        console.log('✅ Card info populated');
    } else {
        console.log('⚠️ No card info to populate');
    }

    // Populate generated images
    const imagesContainer = document.getElementById('generatedImagesDisplay');
    console.log('🖼️ Images container found:', !!imagesContainer);
    console.log('🖼️ Generated images:', generatedImages);

    if (imagesContainer && generatedImages && generatedImages.length > 0) {
        console.log(`🖼️ Displaying ${generatedImages.length} images`);
        imagesContainer.innerHTML = '';

        generatedImages.forEach((imageInfo, index) => {
            const imageItem = document.createElement('div');
            imageItem.className = 'apple-image-item-expanded';

            // Handle different image path formats
            let imagePath;
            if (typeof imageInfo === 'string') {
                imagePath = imageInfo;
            } else if (imageInfo.image_path) {
                imagePath = imageInfo.image_path;
            } else if (imageInfo.path) {
                imagePath = imageInfo.path;
            } else {
                console.warn('⚠️ Unknown image format:', imageInfo);
                imagePath = imageInfo;
            }

            const filename = `ai_image_${index + 1}.jpg`;
            console.log(`🖼️ Adding image ${index + 1}: ${imagePath}`);

            imageItem.innerHTML = `
                <div class="apple-image-display-expanded">
                    <img src="/${imagePath}" alt="Generated AI Image ${index + 1}" loading="lazy"
                         onerror="console.error('Failed to load image: ${imagePath}')">
                    <div class="apple-image-overlay">
                        AI Generated Image ${index + 1}
                    </div>
                </div>
                <div class="apple-image-actions">
                    <a href="/${imagePath}" download="${filename}" class="apple-download-btn">
                        📥 Tải về
                    </a>
                </div>
            `;

            imagesContainer.appendChild(imageItem);
        });

        console.log('✅ Images populated successfully');
    } else if (imagesContainer) {
        console.log('⚠️ No images to display');
        imagesContainer.innerHTML = `
            <div style="text-align: center; padding: var(--spacing-3xl); color: var(--color-text-secondary);">
                <p style="font-size: 18px;">Chưa có ảnh AI nào được tạo</p>
            </div>
        `;
    } else {
        console.error('❌ Images container not found!');
    }
}

// Function to fetch and display result data
function loadResultData() {
    console.log('🔄 Loading result data...');

    fetch('/api/session_status')
        .then(response => {
            console.log('📡 Session status response:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📊 Session data received:', data);

            if (data.status === 'success' && data.session) {
                const cardInfo = data.session.card_info || {};
                const generatedImages = data.session.generated_images || [];

                console.log('📄 Card info:', cardInfo);
                console.log('🖼️ Generated images count:', generatedImages.length);

                populateResultView(cardInfo, generatedImages);
            } else {
                console.log('⚠️ No session data available:', data.message || 'Unknown reason');
                populateResultView({}, []);
            }
        })
        .catch(error => {
            console.error('❌ Error loading result data:', error);
            populateResultView({}, []);
        });
}

// Override the generateAI function to show result view after generation
const originalGenerateAI = window.generateAI;
window.generateAI = function() {
    if (originalGenerateAI) {
        // Call original function
        const result = originalGenerateAI.call(this);

        // If it returns a promise, wait for it to complete
        if (result && typeof result.then === 'function') {
            result.then(() => {
                // After generation is complete, show result view
                setTimeout(() => {
                    if (typeof showResultView === 'function') {
                        showResultView();
                        loadResultData();
                    }
                }, 2000);
            });
        } else {
            // If not a promise, just wait a bit and show result
            setTimeout(() => {
                if (typeof showResultView === 'function') {
                    showResultView();
                    loadResultData();
                }
            }, 2000);
        }

        return result;
    }
};

console.log('✅ Main.js loaded successfully with combined file support');


