google/generativeai/__init__.py,sha256=kmtGLR_9itUQX3UVxYugL7OvW8F7i2_AoPH5lFD-J7g,2486
google/generativeai/__pycache__/__init__.cpython-310.pyc,,
google/generativeai/__pycache__/client.cpython-310.pyc,,
google/generativeai/__pycache__/discuss.cpython-310.pyc,,
google/generativeai/__pycache__/embedding.cpython-310.pyc,,
google/generativeai/__pycache__/generative_models.cpython-310.pyc,,
google/generativeai/__pycache__/models.cpython-310.pyc,,
google/generativeai/__pycache__/operations.cpython-310.pyc,,
google/generativeai/__pycache__/string_utils.cpython-310.pyc,,
google/generativeai/__pycache__/text.cpython-310.pyc,,
google/generativeai/__pycache__/version.cpython-310.pyc,,
google/generativeai/client.py,sha256=m2FrDOHHUT9-3Dy-ahXsP2W7V1OmboVvkZuaH2t1NIM,9425
google/generativeai/discuss.py,sha256=cvXXGpqJGUKQs0GqYLwdj84JwSBEfXffDI_ezr0HI5M,20171
google/generativeai/embedding.py,sha256=T0HFselPldPpf4ujBR2oSUEDJhwFwsKNmdWCn0R6T_s,6293
google/generativeai/generative_models.py,sha256=2xX1qZ517Bc1wIGK9jD_aZMjnC4U7O1g6Jxh55Xfp4I,18671
google/generativeai/models.py,sha256=zC5zC_Yq86AAZM307gPspXefgFu-Zzl2C3dffCMrrJw,13432
google/generativeai/notebook/__init__.py,sha256=CVZrwI1B6hcVoGkVXk5M1UPPF7mpH7PfSAjjyAnVW4o,1169
google/generativeai/notebook/__pycache__/__init__.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/argument_parser.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/argument_parser_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/cmd_line_parser.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/cmd_line_parser_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/command.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/command_utils.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/compare_cmd.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/compile_cmd.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/eval_cmd.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/flag_def.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/flag_def_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/gspread_client.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/html_utils.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/html_utils_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/input_utils.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/input_utils_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/ipython_env.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/ipython_env_impl.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/magics.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/magics_engine.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/magics_engine_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/model_registry.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/model_registry_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/output_utils.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/parsed_args_lib.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/post_process_utils.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/post_process_utils_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/post_process_utils_test_helper.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/py_utils.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/py_utils_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/run_cmd.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/sheets_id.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/sheets_id_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/sheets_sanitize_url.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/sheets_sanitize_url_test.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/sheets_utils.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/text_model.cpython-310.pyc,,
google/generativeai/notebook/__pycache__/text_model_test.cpython-310.pyc,,
google/generativeai/notebook/argument_parser.py,sha256=akmRzKaDm-_aTGe8cy7a6zYlJpo7Cf2nMZ0FtPf_BwE,3961
google/generativeai/notebook/argument_parser_test.py,sha256=5oNHyDrtRyIhO1W5DQj_wdGyX2fFUG2VcN8RzYDIEbE,1955
google/generativeai/notebook/cmd_line_parser.py,sha256=EbP4Ql2azTCvnEFqD1XANUie-FFCMvrkwE9zzsN1kNc,20533
google/generativeai/notebook/cmd_line_parser_test.py,sha256=lSO7htngwDejZrK61FMFE5jCYW93LYoGvAE73jFnkoc,15888
google/generativeai/notebook/command.py,sha256=BwpHMSSORuZfyCqVeJ9q694dPpmfL2jJrLhnbhWqAHw,1535
google/generativeai/notebook/command_utils.py,sha256=u9F-OrVHHjjQWbCdeHY4TAIMdgseluynDWUK-CA92Ug,6270
google/generativeai/notebook/compare_cmd.py,sha256=_S3yv9DrLicCspBlBYC4JuSCIT4vWDqf1PvQ_8O7PPI,2566
google/generativeai/notebook/compile_cmd.py,sha256=LLGrGYCsG8PFj1DQ1Nu2NK3ihWxVeJ-ClVIM6mrRX3w,2365
google/generativeai/notebook/eval_cmd.py,sha256=t8b-MvrQsvk94CmDyKg6Jn5W4uMhIfSG2wJZyEJu8Tk,2756
google/generativeai/notebook/flag_def.py,sha256=g9MkuXMlRbPnAX79QhQ7wBdTtALjbyed_-qagj8w3qU,17279
google/generativeai/notebook/flag_def_test.py,sha256=_eEh5bFNX0l1SWmgppnT1nNObTnweNksjC1O3st2y0U,15351
google/generativeai/notebook/gspread_client.py,sha256=sgDaDHTDolqdlK32U8XvKWYlBx_WtBT4xLO5mHshuSM,7711
google/generativeai/notebook/html_utils.py,sha256=1VW0U1yaIhvYQ9UGfzfBoey6tdhcSCdzLmCxly_22os,1555
google/generativeai/notebook/html_utils_test.py,sha256=qytO6GHIQipqHE71hjq4BVZWk_85kvGmu6x3qj_wtDw,1911
google/generativeai/notebook/input_utils.py,sha256=rO0ApC7-dRjGJK7dV8od78_CRhSliHRQYFsrXI0cq8o,2819
google/generativeai/notebook/input_utils_test.py,sha256=EuGeCKX7Q3gOjAhi4CH8Mg8ObRDZDLAoQ5DHjUlL_V8,2465
google/generativeai/notebook/ipython_env.py,sha256=XvPFuaBF7SOsKBuftwW7Da0VDOPSbcjKIqq_BDGlSKc,1686
google/generativeai/notebook/ipython_env_impl.py,sha256=bgWDdVS7bSHf1_PB99gF9PD2Eff95diopnVFLfA6DjI,1058
google/generativeai/notebook/lib/__init__.py,sha256=XZJbp_UwFkIG3WxhEGGBxDjdv2pCkZF_n4r8aRJhbrw,598
google/generativeai/notebook/lib/__pycache__/__init__.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llm_function.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llm_function_test.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llmfn_input_utils.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llmfn_inputs_source.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llmfn_output_row.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llmfn_output_row_test.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llmfn_outputs.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llmfn_outputs_test.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llmfn_post_process.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llmfn_post_process_cmds.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/llmfn_post_process_cmds_test.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/model.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/prompt_utils.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/prompt_utils_test.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/unique_fn.cpython-310.pyc,,
google/generativeai/notebook/lib/__pycache__/unique_fn_test.cpython-310.pyc,,
google/generativeai/notebook/lib/llm_function.py,sha256=DF31lN9R9tKggGQklLq_uUpkDVXiWuFlnS9yGqCzmz4,18315
google/generativeai/notebook/lib/llm_function_test.py,sha256=iUpi7pkzUipL8boUaeLfBoVR7XkY4gqAApA8vuWjW3o,16195
google/generativeai/notebook/lib/llmfn_input_utils.py,sha256=bLjxFzmn4KP78idKWpqUJQJCFvG5zWEeWZXzGFyKSTE,2969
google/generativeai/notebook/lib/llmfn_inputs_source.py,sha256=yxrUzHVMdsvriXRd9x9Vv2JZ4QjfvhbeMf9ME3Xc1Xo,2443
google/generativeai/notebook/lib/llmfn_output_row.py,sha256=6PXjNJi7Me2Pl0_xQ-DNahId6TkdpiBhLZ4Lsn2Y7GY,5917
google/generativeai/notebook/lib/llmfn_output_row_test.py,sha256=ulrK6sHObqk0sMoAbQ6eUp9SqYyZfemx7pa9HBs1yiM,5310
google/generativeai/notebook/lib/llmfn_outputs.py,sha256=aXMz9LV_b7VRjI_jo2CpK1_0ikijAH0lDnEUtHzg1q0,8548
google/generativeai/notebook/lib/llmfn_outputs_test.py,sha256=ooU9rhDBBhoY3kaEuMPeF63amDJhe-65ad3E3vy-1Zc,6410
google/generativeai/notebook/lib/llmfn_post_process.py,sha256=X4_TJiBsrE77vlrCr1QBYtbfnPlPIMBiAZBFP0MmuCU,2470
google/generativeai/notebook/lib/llmfn_post_process_cmds.py,sha256=IRZfkhzgPyN9P13zI9EmeELyF_PyJ8PAA6AwuA15MyU,8569
google/generativeai/notebook/lib/llmfn_post_process_cmds_test.py,sha256=2UO26sfRpBvpKVW-CCFUjCn6RC2SjzdVIkVC3m6qLa0,7352
google/generativeai/notebook/lib/model.py,sha256=LlgdUxiY6FwoUkNxAfwFMdkqxBvr4A4bo0YRiP5wOFA,2055
google/generativeai/notebook/lib/prompt_utils.py,sha256=7lVWJjn0eqzhhux70XiXokqsQo7RbOVUkuGN6hepJ38,1264
google/generativeai/notebook/lib/prompt_utils_test.py,sha256=7jWOGzlfErOGq3oJS9nFfMDQxnkSjMNui5dnmjearRk,1537
google/generativeai/notebook/lib/unique_fn.py,sha256=yo1rucNEWEtrJ2rxbAa5HCLr9XF92sHwb7EB8NXYP6Y,1487
google/generativeai/notebook/lib/unique_fn_test.py,sha256=r_rh2zV5pTq6PjHSCQQlmr4QW1Mv6AV7PnwSJEefQCg,1843
google/generativeai/notebook/magics.py,sha256=JtQK_ERkiHM9i53hezA2IcV5_VCGkA3OCkCwmY0We04,4336
google/generativeai/notebook/magics_engine.py,sha256=2pfAI7PUBpdz1eqxhshqmCJk0JQnIkZg3c6KuF5wBSQ,5482
google/generativeai/notebook/magics_engine_test.py,sha256=gwV-guDc4Rv8v7ACgzhbXddhwhv4LNL4ZBePcVltmYc,26880
google/generativeai/notebook/model_registry.py,sha256=cMLfJICxIssmsXp23pLV_8xnSnbWDmOfNwCIjzlDLtU,1919
google/generativeai/notebook/model_registry_test.py,sha256=UIeFyzu33fs01slEPphAGKbx-npg8eIDSqFFLsN1wSY,1276
google/generativeai/notebook/output_utils.py,sha256=QY-1UzR7yiYS5IEfU6vn0s-foLREVXznva2A2fA4kW8,2085
google/generativeai/notebook/parsed_args_lib.py,sha256=dXH02fJkckm24TkWnnJcpqtseYRxekGBMS-QUjF3Bfs,3084
google/generativeai/notebook/post_process_utils.py,sha256=SLa99MH-zGIJn9W3UppWT7aMH5QSu8OugjX0jIVeJec,5803
google/generativeai/notebook/post_process_utils_test.py,sha256=HTjXXc2_SXwmOXXmRSLyofNT-IkpwhDEF6NjS5tAB1s,8722
google/generativeai/notebook/post_process_utils_test_helper.py,sha256=bMusySOKISqIhmd2_49p8103Np1lwdhshp6YuIjLPRY,991
google/generativeai/notebook/py_utils.py,sha256=UACFs9bj-De6ceDAPtvIryNh9SxSk9vqtZNmVobumbY,2073
google/generativeai/notebook/py_utils_test.py,sha256=gdX4XhWKPs7o7pEP-krJfCSz7heHgV38_-b8dpW3mkM,1768
google/generativeai/notebook/run_cmd.py,sha256=tCcfCPHK5NKwD1d7qRjGRWD8LxLldyf8RVOf0jVKn1g,2744
google/generativeai/notebook/sheets_id.py,sha256=coYbKRwPjEoJJePA-1LErnBlTVibQdvT6P9AC87I0Bw,3071
google/generativeai/notebook/sheets_id_test.py,sha256=T5y-I5VhcXJnzaAzU0gr5kIW35FnHiWIhvMR-JiEOvo,2118
google/generativeai/notebook/sheets_sanitize_url.py,sha256=e6oirLNc2e3lf2RZgF4PqDz3gKDqvESUjMDrItOP47Y,2977
google/generativeai/notebook/sheets_sanitize_url_test.py,sha256=D7chF5jTjw_8G4IkeTfvHNTKMfBVmY3x9EN6F-e8M7w,3893
google/generativeai/notebook/sheets_utils.py,sha256=pKGBuiycyDbd1L613B3714v-GmZ1qxoisVXt_DnlEk4,3994
google/generativeai/notebook/text_model.py,sha256=Dfn1nVL76fnQ4OIB25m_REO2bzm7oY-4PzO_VnHzlkA,2306
google/generativeai/notebook/text_model_test.py,sha256=ZWI_u1BO261l8BeB5-sdlZQf2a_LXuAUyp7oLODsmyc,3192
google/generativeai/operations.py,sha256=9c0BRjEwQ8sWUTs1ehJwhbn1UfsYvJvU4FiqF0Zo8HY,4900
google/generativeai/string_utils.py,sha256=vOU4fHULo9O69vtwsZ5DNQGndEV3ZDizX3sytv0ixZg,2441
google/generativeai/text.py,sha256=KtTxWlZCS5yD9cWVU6pdBeQo3eTUnEeWeT34_3ZoXZc,12345
google/generativeai/types/__init__.py,sha256=CDD3JSl1spuKF4K3ZnhX8F5gk_dvdSzWMtbnK3Fjv4Y,1129
google/generativeai/types/__pycache__/__init__.cpython-310.pyc,,
google/generativeai/types/__pycache__/citation_types.cpython-310.pyc,,
google/generativeai/types/__pycache__/content_types.cpython-310.pyc,,
google/generativeai/types/__pycache__/discuss_types.cpython-310.pyc,,
google/generativeai/types/__pycache__/generation_types.cpython-310.pyc,,
google/generativeai/types/__pycache__/model_types.cpython-310.pyc,,
google/generativeai/types/__pycache__/safety_types.cpython-310.pyc,,
google/generativeai/types/__pycache__/text_types.cpython-310.pyc,,
google/generativeai/types/citation_types.py,sha256=M3Nljz1VG_ortaRTlTWjDFbRPlxx1BoHm4UTmKsFhrg,1232
google/generativeai/types/content_types.py,sha256=X-WoAPvV5mY6AgQcKP52umf3O6yXfshqlxTGAxJ_ubk,6912
google/generativeai/types/discuss_types.py,sha256=dE3O2OL7Xdkk-u6Rf6iC9bITPXNgABuBj4j1V5_OsCM,6661
google/generativeai/types/generation_types.py,sha256=KPDL575bJd1mz96cj8fc_8nTVdb48UfrG-5AK7MG5CA,16593
google/generativeai/types/model_types.py,sha256=9riBRp_bqh0I3Ev3A0kKxYN6kjQpJrrWyME9u21S96A,11638
google/generativeai/types/safety_types.py,sha256=UPPPNybfPqvKcLl23NEHM3aRPXXV3Dm4fRwXFNophZY,10291
google/generativeai/types/text_types.py,sha256=jdXBIEV-NNx-njOqLjMiL-YJFt9bkn2T8fVkwqxG-tU,2319
google/generativeai/version.py,sha256=mtHM3BCc950_cqBDDo8JQLsiNEai8SVDWCZmnT5krds,656
google_generativeai-0.3.2-py3.11-nspkg.pth,sha256=xH5gTxc4UipYP3qrbP-4CCHNGBV97eBR4QqhheCvBl4,539
google_generativeai-0.3.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_generativeai-0.3.2.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_generativeai-0.3.2.dist-info/METADATA,sha256=_UkUagVYvBMf3dkXoRC7HuJUU-kwuPOXa2E-DdpYFls,5949
google_generativeai-0.3.2.dist-info/RECORD,,
google_generativeai-0.3.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
google_generativeai-0.3.2.dist-info/WHEEL,sha256=oiQVh_5PnQM0E3gPdiz09WCNmwiHDMaGer_elqB3coM,92
google_generativeai-0.3.2.dist-info/namespace_packages.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
google_generativeai-0.3.2.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
